-- 全局调试标志
_G.isDebug = false

-- 跳过UI界面标志（用于快速启动，使用默认配置）
_G.skipUI = false

-- 主流程
if _G.isDebug then

    -- 调试模式下的测试代码
    print("调试模式启动")


    --加载需要的模块
    require("init.init")
    local utils = require("init.utils")
    local char = require("character")
    local common = require("init.common")

    --启动全局提示器
    _G.hudManager = utils.HUDManager:new()

    --while true do
    --    touchDown(1, 206, 520)
    --    sleep(100)
    --    while true do
    --        touchMove(1,math.random(100,252),math.random(450,600))
    --        sleep(1000)
    --    end
    --    touchUp(1)
    --end

    --print(common.processOcr(47,0,334,67))


else
    -- 正常模式：启动新的自动化系统
    print("996_agent 启动")



    -- 进行初始化
    require("init.init")
    print("初始化完成，配置已合并")

    -- 启动全局提示器
    local utils = require("init.utils")
    _G.hudManager = utils.HUDManager:new()

    -- 启动核心自动化循环
    require("core.automation")()
end

