-- ======================================================================
-- 核心自动化系统
-- 实现游戏自动化脚本的核心逻辑模块
-- ======================================================================

local utils = require("init.utils")
local config = require("config.config")
local character = require("character")
local common = require("init.common")

-- 自动化系统类
local AutomationSystem = {}
AutomationSystem.__index = AutomationSystem

function AutomationSystem:new()
    local instance = {
        -- 状态管理
        isInMap = false,                    -- 是否已进入地图
        lastRestartTime = os.time(),        -- 上次重启时间

        -- 喊话计时器
        shoutTimers = {},                   -- 喊话计时器集合

        -- 定时任务状态
        lastForbiddenTime = 0,              -- 上次禁地执行时间
        lastWorldBossTime = 0,              -- 上次世界首领执行时间
        worldBossFailCount = 0,             -- 世界首领当日失败次数
        lastExecutedTime = "",              -- 上次执行每日任务的时间标记

        -- 每日任务执行状态
        dailyTasksExecuted = {
            personalBoss = false,           -- 个人首领
            enterDao = false,               -- 入道天途
        },

        -- 每日任务时间记录
        lastDailyReset = os.date("%Y-%m-%d"), -- 上次每日重置日期
    }
    setmetatable(instance, AutomationSystem)
    return instance
end

-- 初始化喊话计时器
function AutomationSystem:initShoutTimers()
    local userConfig = config.userConfig
    if not userConfig.喊话设置 then return end
    
    for key, setting in pairs(userConfig.喊话设置) do
        if setting.enabled and setting.interval and setting.content then
            self.shoutTimers[key] = {
                timer = utils.Timer:new(),
                interval = setting.interval,
                content = setting.content,
                enabled = setting.enabled
            }
            print("初始化喊话计时器: " .. key .. ", 间隔: " .. setting.interval .. "秒")
        end
    end
end

-- 检查并重置每日任务状态
function AutomationSystem:checkDailyReset()
    local currentDate = os.date("%Y-%m-%d")
    if currentDate ~= self.lastDailyReset then
        print("检测到新的一天，重置每日任务状态")
        self.lastDailyReset = currentDate
        self.dailyTasksExecuted.personalBoss = false
        self.dailyTasksExecuted.enterDao = false
        self.worldBossFailCount = 0
        self.lastExecutedTime = "" -- 重置每日任务执行时间标记
    end
end

-- 进入地图
function AutomationSystem:enterMap()
    local userConfig = config.userConfig
    
    -- 检查是否启用进入地图功能
    if not userConfig.map_enabled or not userConfig.map_enabled.enable then
        print("进入地图功能未启用")
        return true
    end
    
    local mapName = userConfig.map
    if not mapName then
        print("未配置地图名称")
        return false
    end
    
    print("尝试进入地图: " .. mapName)
    local success = character:进入打宝地图(mapName)
    
    if success then
        print("成功进入地图: " .. mapName)
        self.isInMap = true
        return true
    else
        print("进入地图失败: " .. mapName)
        self.isInMap = false
        return false
    end
end

-- 检查定时重启
function AutomationSystem:checkTimedRestart()
    local userConfig = config.userConfig
    
    -- 检查定时重启是否启用
    if not userConfig.定时重启 or not userConfig.定时重启.enable then
        return false
    end
    
    local restartInterval = userConfig.定时重启.重启时间 * 60 -- 转换为秒
    local currentTime = os.time()
    
    if currentTime - self.lastRestartTime >= restartInterval then
        print("达到定时重启时间，执行重启流程")
        return true
    end
    
    return false
end

-- 启动游戏应用
function AutomationSystem:startGameApp()
    local packageName = config.开发设置.应用包名

    if packageName then
        print("启动游戏应用: " .. packageName)
        utils.start_app(packageName)
        sleep(3000) -- 等待应用启动
        return true
    else
        print("未找到应用包名配置")
        return false
    end
end

-- 执行重启流程
function AutomationSystem:executeRestart()
    print("开始执行重启流程")

    -- 1. 关闭应用
    local closeSuccess = character:closeApp()
    if not closeSuccess then
        print("关闭应用失败")
        return false
    end

    -- 2. 等待6秒
    print("等待6秒...")
    sleep(6000)

    -- 3. 启动游戏应用
    local startSuccess = self:startGameApp()
    if not startSuccess then
        print("启动游戏应用失败")
        return false
    end

    -- 4. 重新加载游戏
    print("重新加载游戏")
    local loadSuccess = character:加载游戏()

    if loadSuccess then
        print("游戏重新加载成功")
        self.lastRestartTime = os.time()
        self.isInMap = false -- 重启后需要重新进图
        return true
    else
        print("游戏重新加载失败")
        return false
    end
end

-- 处理喊话
function AutomationSystem:handleShouting()
    for key, shoutData in pairs(self.shoutTimers) do
        if shoutData.enabled and shoutData.timer:elapsed("s") >= shoutData.interval then
            print("执行喊话: " .. key .. " - " .. shoutData.content)
            local success = character:执行喊话(shoutData.content)
            if success then
                shoutData.timer:reset()
                print("喊话执行成功: " .. key)
            else
                print("喊话执行失败: " .. key .. "，将在下次循环重试")
            end
        end
    end
end

-- 主线挂机逻辑
function AutomationSystem:mainHangUp()
    -- 开启目标选择怪物界面
    character:开启目标选择怪物界面()
    
    -- 攻击第一个目标怪物
    character:攻击第一个目标怪物()
    
    -- 开启自动战斗功能
    character:开启自动战斗功能()
    
    -- 处理喊话
    self:handleShouting()
end

-- 检查周期性任务
function AutomationSystem:checkPeriodicTasks()
    local currentTime = os.time()
    local userConfig = config.userConfig
    
    -- 检查禁地任务（每6小时执行一次）
    if userConfig.禁地 and userConfig.禁地.enable then
        if currentTime - self.lastForbiddenTime >= 6 * 3600 then -- 6小时
            print("执行禁地任务")
            local success = character:禁地()
            if success then
                self.lastForbiddenTime = currentTime
                self.isInMap = false -- 执行任务后需要重新进图
                print("禁地任务执行完成")
            else
                print("禁地任务执行失败")
            end
        end
    end
    
    -- 检查世界首领任务（每15分钟执行一次，当天失败2次后不再执行）
    if userConfig.世界Boss and userConfig.世界Boss.enable and self.worldBossFailCount < 2 then
        if currentTime - self.lastWorldBossTime >= 15 * 60 then -- 15分钟
            print("执行世界首领任务")
            local success = character:世界首领()
            if success then
                self.lastWorldBossTime = currentTime
                print("世界首领任务执行完成")
            else
                self.worldBossFailCount = self.worldBossFailCount + 1
                print("世界首领任务执行失败，失败次数: " .. self.worldBossFailCount)
            end
            self.isInMap = false -- 执行任务后需要重新进图
        end
    end
end

-- 检查每日定时任务
function AutomationSystem:checkDailyTasks()
    local currentHour = tonumber(os.date("%H"))
    local currentMinute = tonumber(os.date("%M"))
    local userConfig = config.userConfig

    -- 为了避免在同一分钟内重复执行，添加执行标记
    local timeKey = string.format("%02d:%02d", currentHour, currentMinute)
    if self.lastExecutedTime == timeKey then
        return
    end

    local taskExecuted = false

    -- 每天22:00执行领取宝箱和散人福利
    if currentHour == 22 and currentMinute == 0 then
        if userConfig.升级宝箱 and userConfig.升级宝箱.enable then
            print("执行领取宝箱任务")
            character:领取宝箱()
            self.isInMap = false
            taskExecuted = true
        end

        if userConfig.散人福利 and userConfig.散人福利.enable then
            print("执行散人福利任务")
            character:领取散人福利()
            self.isInMap = false
            taskExecuted = true
        end
    end

    -- 每天20:00执行星空秘境
    if currentHour == 20 and currentMinute == 0 then
        if userConfig.星空秘境 and userConfig.星空秘境.enable then
            print("执行星空秘境任务")
            character:星空秘境()
            self.isInMap = false
            taskExecuted = true
        end
    end

    -- 每天13:00和21:00执行武林争霸
    if (currentHour == 13 or currentHour == 21) and currentMinute == 0 then
        if userConfig.武林争霸 and userConfig.武林争霸.enable then
            print("执行武林争霸任务")
            character:武林争霸()
            self.isInMap = false
            taskExecuted = true
        end
    end

    -- 如果执行了任务，记录时间避免重复执行
    if taskExecuted then
        self.lastExecutedTime = timeKey
    end
end

-- 执行启动时一次性任务
function AutomationSystem:executeStartupTasks()
    local userConfig = config.userConfig
    
    -- 执行个人首领
    if userConfig.个人首领 and userConfig.个人首领.enable and not self.dailyTasksExecuted.personalBoss then
        print("执行个人首领任务")
        local success = character:个人首领()
        if success then
            self.dailyTasksExecuted.personalBoss = true
            print("个人首领任务执行完成")
        else
            print("个人首领任务执行失败")
        end
        self.isInMap = false
    end
    
    -- 执行入道天途
    if userConfig.入道天途 and userConfig.入道天途.enable and not self.dailyTasksExecuted.enterDao then
        print("执行入道天途任务")
        character:入道天途()
        self.dailyTasksExecuted.enterDao = true
        self.isInMap = false
        print("入道天途任务执行完成")
    end
end

-- 主循环
function AutomationSystem:run()
    print("核心自动化系统启动")
    
    -- 初始化
    self:initShoutTimers()
    
    -- 执行启动时一次性任务
    self:executeStartupTasks()
    
    -- 主循环
    while true do
        -- 检查每日重置
        self:checkDailyReset()
        
        -- 确保已进入地图
        if not self.isInMap then
            print("检测到未进入地图状态，尝试进入地图")
            if not self:enterMap() then
                print("进入地图失败，等待5秒后重试")
                sleep(5000)
                goto continue
            end
        end
        
        -- 主线挂机逻辑
        self:mainHangUp()
        
        -- 检查定时重启（只在挂机过程中检查）
        if self:checkTimedRestart() then
            if self:executeRestart() then
                goto continue -- 重启成功，重新开始循环
            end
        end
        
        -- 检查周期性任务
        self:checkPeriodicTasks()
        
        -- 检查每日定时任务
        self:checkDailyTasks()
        
        ::continue::
        sleep(1000) -- 主循环间隔
    end
end

-- 创建并返回自动化系统实例
local function createAutomationSystem()
    local system = AutomationSystem:new()
    return function()
        system:run()
    end
end

return createAutomationSystem
