local U = {}

-- ==================== 统一计时器类 ====================
U.Timer = {}
U.Timer.__index = U.Timer

-- 时间单位转换表
local TIME_UNITS = {
    ms = 1,           -- 毫秒
    s = 1000,         -- 秒
    sec = 1000,       -- 秒（别名）
    m = 60000,        -- 分钟
    min = 60000,      -- 分钟（别名）
    h = 3600000,      -- 小时
    hour = 3600000    -- 小时（别名）
}

-- 内部函数：转换时间到毫秒
local function convertToMs(duration, unit)
    if not duration then return nil end
    
    unit = unit or "s"  -- 默认单位是秒
    local multiplier = TIME_UNITS[unit]
    
    if not multiplier then
        error("不支持的时间单位: " .. tostring(unit) .. "。支持的单位: ms, s/sec, m/min, h/hour")
    end
    
    return duration * multiplier
end

-- 标准的构造函数

function U.Timer:new(duration, unit)
    local o = {
        _startTime = tickCount(),
        _pausedAt = nil,
        _pausedTime = 0,
        _isPaused = false,
        _duration = convertToMs(duration, unit)  -- 内部统一使用毫秒
    }
    setmetatable(o, self)
    return o
end

function U.Timer:reset(newDuration, unit)
    if newDuration ~= nil then
        self._duration = convertToMs(newDuration, unit)
    end
    self._startTime = tickCount()
    self._pausedAt = nil
    self._pausedTime = 0
    self._isPaused = false
end

function U.Timer:pause()
    if not self._isPaused then
        self._pausedAt = tickCount()
        self._isPaused = true
    end
end

function U.Timer:resume()
    if self._isPaused then
        self._pausedTime = self._pausedTime + (tickCount() - self._pausedAt)
        self._pausedAt = nil
        self._isPaused = false
    end
end

function U.Timer:elapsed(unit)
    local elapsedMs
    if self._isPaused then
        elapsedMs = self._pausedAt - self._startTime - self._pausedTime
    else
        elapsedMs = tickCount() - self._startTime - self._pausedTime
    end
    
    -- 如果指定了单位，转换返回值
    if unit and TIME_UNITS[unit] then
        return elapsedMs / TIME_UNITS[unit]
    end
    
    return elapsedMs  -- 默认返回毫秒
end

function U.Timer:remaining(unit)
    if not self._duration then
        return nil  -- 秒表模式没有剩余时间概念
    end
    
    local elapsed = self:elapsed()
    local remaining = self._duration - elapsed
    remaining = math.max(0, remaining)
    
    -- 如果指定了单位，转换返回值
    if unit and TIME_UNITS[unit] then
        return remaining / TIME_UNITS[unit]
    end
    
    return remaining  -- 默认返回毫秒
end

function U.Timer:progress()
    if not self._duration then
        return nil  -- 秒表模式没有进度概念
    end
    
    local elapsed = self:elapsed()
    return math.min(1.0, elapsed / self._duration)
end

function U.Timer:isFinished()
    if not self._duration then
        return false  -- 秒表模式永不完成
    end
    
    return self:remaining() <= 0
end

-- ==================== 深度拷贝函数 ====================
-- @param original 需要拷贝的表或值
-- @param memo (可选) 用于处理循环引用的内部表
-- @return 返回一个全新的、与原始值完全独立的拷贝
function U.deepCopy(original, memo)
    memo = memo or {}

    -- 非 table 类型直接返回值本身
    if type(original) ~= 'table' then
        return original
    end

    -- 如果这个 table 已经被拷贝过，直接返回拷贝后的引用，避免循环引用导致的死循环
    if memo[original] then
        return memo[original]
    end

    local copy = {}
    memo[original] = copy -- 记录新创建的拷贝

    -- 递归地拷贝键和值
    for k, v in pairs(original) do
        copy[U.deepCopy(k, memo)] = U.deepCopy(v, memo)
    end

    -- 拷贝元表
    return setmetatable(copy, getmetatable(original))
end


-- ==================== 两点距离函数 ====================
-- @param x1, y1 第一个点的坐标
-- @param x2, y2 第二个点的坐标
-- @return 返回两点之间的欧几里得距离
function U.distance(x1, y1, x2, y2)
    local dx = x2 - x1
    local dy = y2 - y1
    return math.sqrt(dx*dx + dy*dy)
end

-- ==================== 带日志的延时函数 ====================
-- @param ms 延时时间（毫秒）
-- @param tag 可选的标签，用于日志输出
function U.sleepLog(ms, tag)
    if tag then 
        print(string.format("[%s] sleep %dms", tag, ms)) 
    end
    sleep(ms)
end

-- ==================== U.HUDManager类 OOP实现 ====================
U.HUDManager = {}
U.HUDManager.__index = U.HUDManager

-- 构造函数
function U.HUDManager:new(config)
    config = config or {}

    local instance = setmetatable({}, self)

    instance._hudConfig = {
        text = config.text or "等待脚本运行...",
        fontSize = config.fontSize or 8,
        textColor = config.textColor or "0xFFFF8500",
        bgColor = config.bgColor or "0xD9262626",
        pos = config.pos or 0,
        x = config.x or 0,
        y = config.y or 720,
        width = config.width or 400,
        height = config.height or 30,
        padding = config.padding or {3, 3, 3, 3},
        align_text = config.align_text or 1
    }
    instance._hudId = createHUD()
    instance._isDestroyed = false

    instance:_render()
    return instance
end

function U.HUDManager:_render()
    if self._isDestroyed then
        return false
    end
    local cfg = self._hudConfig
    showHUD(self._hudId, cfg.text, cfg.fontSize, cfg.textColor, cfg.bgColor,
            cfg.pos, cfg.x, cfg.y, cfg.width, cfg.height,
            cfg.padding[1], cfg.padding[2], cfg.padding[3], cfg.padding[4],
            cfg.align_text)
    return true
end

function U.HUDManager:update(newConfig)
    if self._isDestroyed then
        print("警告：HUD已被销毁，无法更新")
        return false
    end

    if type(newConfig) ~= "table" then
        print("警告：update参数必须是配置表")
        return false
    end

    for key, value in pairs(newConfig) do
        if self._hudConfig[key] ~= nil then
            self._hudConfig[key] = value
        end
    end

    return self:_render()
end

function U.HUDManager:destroy()
    if not self._isDestroyed then
        hideHUD(self._hudId)
        self._isDestroyed = true
        return true
    end
    return false
end

-- ==================== 时间工具类 TimeUtils ====================
-- 静态函数表，提供常用的时间计算功能，避免重复的时间算法

U.TimeUtils = {
    -- 检查是否超时
    -- @param startTime 开始时间戳 (os.time())
    -- @param timeoutSeconds 超时秒数
    -- @return boolean 是否已超时
    isTimeout = function(startTime, timeoutSeconds)
        if not startTime or not timeoutSeconds then return false end
        return os.time() - startTime >= timeoutSeconds
    end,
    
    -- 获取剩余时间
    -- @param startTime 开始时间戳 (os.time()) 
    -- @param timeoutSeconds 总超时秒数
    -- @return number 剩余秒数，最小为0
    remaining = function(startTime, timeoutSeconds)
        if not startTime or not timeoutSeconds then return 0 end
        return math.max(0, timeoutSeconds - (os.time() - startTime))
    end,
    
    -- 检查冷却时间是否就绪
    -- @param lastTime 上次时间戳 (os.time())，可以为nil或0
    -- @param cooldownSeconds 冷却秒数
    -- @return boolean 冷却是否已结束
    isCooldownReady = function(lastTime, cooldownSeconds)
        if not lastTime or lastTime == 0 then return true end
        if not cooldownSeconds then return true end
        return os.time() - lastTime >= cooldownSeconds
    end,
    
    -- 格式化时间显示
    -- @param seconds 秒数
    -- @return string 格式化的时间字符串 (如 "1.5m" 或 "30s")
    formatTime = function(seconds)
        if not seconds then return "0s" end
        if seconds >= 60 then
            return string.format("%.1fm", seconds / 60)
        else
            return string.format("%ds", math.floor(seconds))
        end
    end,
    
    -- 检测是否卡住 (位置在指定时间内变化小于阈值)
    -- @param positionHistory 位置历史数组 [{x, y, time}, ...]
    -- @param timeThreshold 时间阈值（秒）
    -- @param distanceThreshold 距离阈值
    -- @return boolean 是否卡住
    isStuck = function(positionHistory, timeThreshold, distanceThreshold)
        if not positionHistory or #positionHistory < 2 then return false end
        if not timeThreshold or not distanceThreshold then return false end
        
        local latest = positionHistory[#positionHistory]
        local oldest = positionHistory[1]
        
        if not latest.time or not oldest.time then return false end
        
        local timeDiff = os.time() - oldest.time
        local distance = U.distance(latest.x, latest.y, oldest.x, oldest.y)
        
        return timeDiff >= timeThreshold and distance < distanceThreshold
    end,
    
    -- 获取已经过的时间
    -- @param startTime 开始时间戳 (os.time())
    -- @return number 已过的秒数
    elapsed = function(startTime)
        if not startTime then return 0 end
        return os.time() - startTime
    end
}


-- ==================== 应用控制工具函数 ====================

function U.getFrontAppName()
    local r = exec("uiautomator dump /sdcard/ui.xml > /dev/null 2>&1 && grep 'package=' /sdcard/ui.xml | head -n 1 | sed 's/.*package=\"//' | sed 's/\".*//'")
    if r ~= nil then
        print(r)
    end
    return r
end

function U.start_app(package_name)
    print("正在动态启动应用: " .. package_name)

    print("正在查询主Activity...")
    local resolve_command = "cmd package resolve-activity --brief " .. package_name
    local result = exec(resolve_command)

    if result == nil or result == "" then
        print("查询主Activity失败，命令没有返回结果。")
        return
    end

    print("查询到的原始结果: \n" .. result)

    local component_name = string.match(result, "([%w_.]+/[%w_.]+)")

    if component_name == nil then
        print("【失败】从查询结果中未能用正则表达式解析出组件名。")
        return
    end

    print("【成功】精确解析到组件名: " .. component_name)

    local start_command = "am start " .. component_name
    print("执行最终动态启动命令: " .. start_command)
    local r = exec(start_command)

    if r ~= nil and r ~= "" then
        print("命令执行结果: " .. r)
    end

    print("动态启动命令已发送。")
end

function U.close_app(package_name)
    print("正在关闭应用: " .. package_name)
    stopApp(package_name)
    print("应用已关闭")
end

return U