
local M = {}

--[[
 * OCR处理主函数
 * @param useOnnx 是否使用ONNX模型（true: ONNX模型, false: NCNN模型）
 * @param left 截图区域左坐标
 * @param top 截图区域上坐标
 * @param right 截图区域右坐标
 * @param bottom 截图区域下坐标
 * @return table|nil 识别结果表，失败返回nil
]]

function M.processOcr(left, top, right, bottom)
  -- 记录开始时间
  local startTime = tickCount()

  -- 2. 获取屏幕截图
  print("正在获取屏幕截图...")
  local bitmap = LuaEngine.snapShot(left, top, right, bottom)
  if not bitmap then
    print("错误：获取屏幕区域失败")
    return nil
  end

  -- 3. 执行OCR识别（确保在finally块释放资源）
  local result
  local success, err = pcall(function()
    -- 执行OCR检测
    local jsonStr = PaddleOcr.detectWithPadding(bitmap, 50, 0xff, 0xff, 0xff)
    print("DEBUG: OCR原始JSON结果：" .. tostring(jsonStr))
    if not jsonStr or jsonStr == "" then
      error("OCR检测返回空结果")
    end

    -- 解析JSON结果
    -- 注意：这里假设您有一个名为 jsonLib 的库来解析JSON。
    local parseSuccess, parsed = pcall(jsonLib.decode, jsonStr)
    if not parseSuccess then
      error("JSON结果解析失败："..tostring(parsed))
    end

    result = parsed
  end)

  -- 4. 确保释放bitmap资源
  LuaEngine.releaseBmp(bitmap)

  if not success then
    print("OCR处理错误："..tostring(err))
    return nil
  end

  -- 5. 输出性能信息
  print(string.format("OCR处理完成，耗时 %.2f 毫秒", tickCount() - startTime))

  return result
end

--[[
 * 在指定区域内查找特定文字，并返回其中心点坐标。
 * @param textToFind string 需要查找的文字。
 * @param areaTable table 包含截图区域坐标的索引表，格式为 {left, top, right, bottom}。
 * @param exactMatch boolean 是否完全匹配，默认true（完全匹配），false为模糊匹配。
 * @return table, boolean | boolean 如果找到，返回中心点坐标表 {x, y} 和 true；如果未找到，返回 false。
]]
function M.findTextAndGetCenter(textToFind, areaTable, exactMatch)
  -- 设置默认值，如果没有传入exactMatch参数，则默认为完全匹配
  if exactMatch == nil then
    exactMatch = true
  end
  
  -- 从表中解构坐标
  local left, top, right, bottom = areaTable[1], areaTable[2], areaTable[3], areaTable[4]

  -- 检查传入的表是否有效
  if not (left and top and right and bottom) then
    print("错误: 传入的坐标表格式不正确或不完整。应为 {left, top, right, bottom}")
    return false
  end

  -- 1. 调用OCR函数获取识别结果
  local ocrResult = M.processOcr(left, top, right, bottom)

  -- 2. 检查OCR是否成功执行并返回了结果
  if not ocrResult then
    print(string.format("在区域 (%d, %d, %d, %d) 内执行OCR失败。", left, top, right, bottom))
    return false
  end

  -- 3. 遍历所有识别出的文字项
  for _, item in ipairs(ocrResult) do
    -- 根据exactMatch参数决定使用完全匹配还是模糊匹配
    local isMatch = false
    if item and item.label then
      if exactMatch then
        -- 完全匹配
        isMatch = (item.label == textToFind)
      else
        -- 模糊匹配（包含匹配）
        isMatch = (string.find(item.label, textToFind) ~= nil)
      end
    end
    
    if isMatch then
      -- 4. 如果找到匹配项，计算中心点的真实坐标
      local centerX = left + item.x + (item.w / 2)
      local centerY = top + item.y + (item.h / 2)

      print(string.format("成功找到文字 '%s'!", textToFind))

      -- 5. 返回包含中心点坐标的 table 和布尔值 true
      return {x = centerX, y = centerY}, true
    end
  end

  -- 6. 如果遍历完所有结果都未找到，则返回 false
  print(string.format("在指定区域未找到文字 '%s'。", textToFind))
  return false
end

--[[
 * 在指定区域内查找特定文字，并执行指定动作。
 * @param textToFind string 需要查找的文字。
 * @param areaTable table 包含截图区域坐标的索引表，格式为 {left, top, right, bottom}。
 * @param action string|table|function 执行的动作：
 *   - "tap": 点击找到的文字中心位置
 *   - {x, y}: 点击指定坐标，如 {100, 200}
 *   - function: 自定义函数，会传入找到的文字中心点
 * @param exactMatch boolean 是否完全匹配，默认true（完全匹配），false为模糊匹配。
 * @param waitTime number 执行完成后的等待时间（毫秒）。
 * @return boolean 如果找到并执行成功返回 true，否则返回 false。
]]
function M.findTextAndExecute(textToFind, areaTable, action, exactMatch, waitTime)
  -- 调用原有的查找函数，传递exactMatch参数
  local centerPoint, isFound = M.findTextAndGetCenter(textToFind, areaTable, exactMatch)

  if isFound then
    print(string.format("找到文字 '%s'，准备执行动作", textToFind))

    -- 根据action类型执行不同的操作
    if type(action) == "string" and action == "tap" then
      -- 执行点击操作（点击找到的文字位置）
      print(string.format("点击坐标: (%.2f, %.2f)", centerPoint.x, centerPoint.y))
      tap(centerPoint.x, centerPoint.y)
    elseif type(action) == "table" and #action == 2 then
      -- 执行点击操作（点击指定坐标）
      print(string.format("点击指定坐标: (%d, %d)", action[1], action[2]))
      tap(action[1], action[2])
    elseif type(action) == "function" then
      -- 执行自定义函数
      print("执行自定义动作函数")
      action(centerPoint)
    else
      print("错误: 无效的动作参数，应为 'tap'、{x, y} 坐标表或函数")
      return false
    end

    -- 等待指定时间
    if waitTime and waitTime > 0 then
      print(string.format("等待 %d 毫秒", waitTime))
      sleep(waitTime)
    end

    return true
  else
    print(string.format("未找到文字 '%s'，跳过执行", textToFind))
    return false
  end
end

--[[
 * 在指定区域内查找多点颜色匹配。
 * @param colorParams table 完整参数表，格式为 {left, top, right, bottom, firstColor, offsetColor, dir, sim}
 * @param findAll boolean 是否查找所有匹配点，默认false（单匹配）
 * @return table|boolean 单匹配返回坐标{x,y}，全匹配返回坐标列表，否则返回 false
]]
function M.findMultiColor(colorParams, findAll)
  -- 默认为单匹配
  if findAll == nil then
    findAll = false
  end
  
  if findAll then
    -- 全匹配模式
    local result = findMultiColorAllT(colorParams)
    
    if result ~= nil and #result > 0 then
      print(string.format("找到 %d 个颜色匹配", #result))
      return result
    else
      print("未找到指定颜色")
      return false
    end
  else
    -- 单匹配模式
    local x, y = findMultiColorT(colorParams)
    
    if x ~= -1 and y ~= -1 then
      print(string.format("找到颜色匹配，位置: (%d, %d)", x, y))
      return {x = x, y = y}
    else
      print("未找到指定颜色")
      return false
    end
  end
end

--[[
 * 在指定区域内查找多点颜色，并执行指定动作。
 * @param colorParams table findMultiColorT 的完整参数表，格式为 {left, top, right, bottom, firstColor, offsetColor, dir, sim}
 * @param action string|table|function 执行的动作：
 *   - "tap": 点击找到的颜色位置
 *   - {x, y}: 点击指定坐标，如 {100, 200}
 *   - function: 自定义函数，会传入找到的坐标 {x = x, y = y}
 * @param waitTime number 执行完成后的等待时间（毫秒）。
 * @return boolean 如果找到并执行成功返回 true，否则返回 false。
]]
function M.findMultiColorAndExecute(colorParams, action, waitTime)
  local x, y = -1, -1
  x, y = findMultiColorT(colorParams)
  
  if x ~= -1 and y ~= -1 then
    print(string.format("找到颜色，位置: (%d, %d)", x, y))
    
    -- 根据action类型执行不同的操作
    if type(action) == "string" and action == "tap" then
      -- 执行点击操作（点击找到的颜色位置）
      print(string.format("点击颜色坐标: (%d, %d)", x, y))
      tap(x, y)
    elseif type(action) == "table" and #action == 2 then
      -- 执行点击操作（点击指定坐标）
      print(string.format("点击指定坐标: (%d, %d)", action[1], action[2]))
      tap(action[1], action[2])
    elseif type(action) == "function" then
      -- 执行自定义函数
      print("执行自定义动作函数")
      action({x = x, y = y})
    else
      print("错误: 无效的动作参数，应为 'tap'、{x, y} 坐标表或函数")
      return false
    end

    -- 等待指定时间
    if waitTime and waitTime > 0 then
      print(string.format("等待 %d 毫秒", waitTime))
      sleep(waitTime)
    end

    return true
  else
    print("未找到指定颜色，跳过执行")
    return false
  end
end

--[[
 * 检查指定区域内特定颜色的数量是否在指定范围内。
 * @param params table 包含检测参数的表，格式为 {left, top, right, bottom, color, similarity}
 * @param threshold number|table 阈值，可以是数字（大于等于）或表{min, max}（范围检查）
 * @return boolean 如果颜色数量满足条件返回 true，否则返回 false
]]
function M.checkColorCount(params, threshold)
  -- 从表中解构参数
  local left, top, right, bottom, color, similarity = 
        params[1], params[2], params[3], params[4], params[5], params[6]
  
  -- 检查参数有效性
  if not (left and top and right and bottom and color and similarity) then
    print("错误: 参数表格式不正确，应为 {left, top, right, bottom, color, similarity}")
    return false
  end
  
  if not threshold then
    print("错误: 缺少阈值参数")
    return false
  end
  
  -- 获取颜色数量
  local count = getColorNum(left, top, right, bottom, color, similarity)
  
  -- 根据阈值类型进行判断
  if type(threshold) == "number" then
    -- 简单阈值：大于等于
    print(string.format("DEBUG: 检测到颜色 %s 数量: %d, 阈值: >= %d", color, count, threshold))
    return count >= threshold
  elseif type(threshold) == "table" and threshold.min and threshold.max then
    -- 范围阈值：在min和max之间
    print(string.format("DEBUG: 检测到颜色 %s 数量: %d, 范围: %d-%d", color, count, threshold.min, threshold.max))
    return count >= threshold.min and count <= threshold.max
  else
    print("错误: 阈值参数格式不正确，应为数字或{min=x, max=y}")
    return false
  end
end

--[[
 * 在指定区域内查找所有图片匹配点。
 * @param picParams table findPicAllPoint 的完整参数表，格式为 {left, top, right, bottom, picPath, similarity}
 * @return table|boolean 如果找到返回坐标列表，否则返回 false
]]
function M.findPicAllPoint(picParams)
  local left, top, right, bottom, picPath, similarity = 
        picParams[1], picParams[2], picParams[3], picParams[4], picParams[5], picParams[6]
  
  -- 检查参数有效性
  if not (left and top and right and bottom and picPath and similarity) then
    print("错误: 参数表格式不正确，应为 {left, top, right, bottom, picPath, similarity}")
    return false
  end
  
  local result = findPicAllPoint(left, top, right, bottom, picPath, similarity)
  
  if result ~= nil and #result > 0 then
    print(string.format("找到 %d 个图片匹配点", #result))
    return result
  else
    print("未找到指定图片")
    return false
  end
end

return M
