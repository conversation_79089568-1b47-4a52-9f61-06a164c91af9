-- ======================================================================
-- 配置示例文件
-- 展示如何自定义各种功能的配置
-- ======================================================================

-- 这是一个配置示例，展示了如何根据需要调整各种设置

local exampleConfig = {
    -- 定时重启配置
    定时重启 = {
        enable = true,          -- 启用定时重启
        重启时间 = 45           -- 每45分钟重启一次
    },

    -- 地图配置
    map_enabled = { enable = true },    -- 启用自动进图
    map = "离火圣殿二层",               -- 选择离火圣殿二层地图

    -- 喊话配置 - 可以配置多个喊话内容
    喊话设置 = {
        喊话1 = {
            enabled = true,
            content = "专业代练，价格公道，安全可靠！",
            interval = 30       -- 每30秒喊话一次
        },
        
        喊话2 = {
            enabled = true,
            content = "收购各种稀有材料，高价回收！",
            interval = 45       -- 每45秒喊话一次
        },
        
        -- 可以添加更多喊话配置
        喊话3 = {
            enabled = false,    -- 暂时禁用
            content = "出售极品装备，物美价廉！",
            interval = 60
        }
    },

    -- 任务功能开关 - 根据需要启用或禁用
    禁地 = { 
        enable = true,
        层数 = { "1层", "2层", "3层" }  -- 只挑战前3层
    },
    
    世界Boss = { enable = true },       -- 启用世界首领
    
    个人首领 = { 
        enable = true,
        分配次数 = { 
            元素 = 3,   -- 元素首领3次
            装备 = 2,   -- 装备首领2次
            龙脉 = 4,   -- 龙脉首领4次
            神器 = 1    -- 神器首领1次
        }
    },
    
    入道天途 = { enable = true },       -- 启用入道天途
    升级宝箱 = { enable = true },       -- 启用宝箱领取
    散人福利 = { enable = true },       -- 启用散人福利
    星空秘境 = { enable = false },      -- 暂时禁用星空秘境
    武林争霸 = { enable = true }        -- 启用武林争霸
}

-- 使用说明：
-- 1. 复制需要的配置到 config/config.lua 的 userConfig 部分
-- 2. 根据实际需要调整各项参数
-- 3. 保存文件后重新运行脚本

print("配置示例加载完成")
print("请参考此文件来自定义您的配置")

return exampleConfig
