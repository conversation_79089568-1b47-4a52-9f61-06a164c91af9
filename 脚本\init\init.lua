
local function loadClientConfig()
    local clientConfig = {
        base_url = "http://47.94.99.3/api/client",
        app_id = "APP_ME8JS04C_HOUBHU",
        crypto_key = "3a990e26d6a1e62f12527da8b6ac7d1e",
        card_key = "null",
        machine_code = getDeviceId(),
        version = "1.0.0",                 -- 当前版本号
        heartbeat_interval = 60,            -- 心跳间隔（秒）
        max_heartbeat_fails = 3            -- 最大允许失败次数
    }

    return clientConfig
end

local function loadClass()
    print("准备加载类")
    --客户端post请求需要的类
    import('java.lang.*')
    import('java.util.*')
    import('com.nx.assist.lua.LuaEngine')

    --这是LuaEngine.snapShot截图需要的类
    import('android.*')
    import('java.lang.*')
    import('java.util.*')
    import('android.widget.*')
    import('android.os.Build')
    import('android.graphics.*');
    import('com.nx.assist.lua.LuaEngine')

    --这是PaddleOcr需要的类
    import('java.io.File')
    import('java.lang.*')
    import('java.util.Arrays')
    import('android.content.Context')
    import('com.nx.assist.lua.LuaEngine')
    import('com.nx.assist.lua.PaddleOcr')

    print("类加载成功")
end

local function ConfigurationInterface()
    print("准备加载用户配置界面")
    local uiConfig = require("init.ui")
    print("用户配置界面加载完成")
    return uiConfig
end

local function initPaddleOcr()
    print(("准备加载 PaddleOcr 的 ONNX 模型"))
    local useOnnx = true
    if not PaddleOcr.loadModel(useOnnx) then
        print("ERROR: PaddleOcr 模型加载失败")
        return nil
    else
        print("PaddleOcr 模型加载成功")
    end
end

local function main()

    --加载必需的类
    loadClass()

    --加载客户端配置并初始化客户端类
    local apiClient = require("init.apiClient")
    _G.client = apiClient.new(loadClientConfig())


    local uiConfig
    
    -- 保护措施：面向用户发布时，强制重置调试标志
    if not checkIsDebug() then
        _G.isDebug = false
        _G.skipUI = false
        print("面向用户模式：已重置所有调试标志")
    end
    
    -- UI 展示前先进行一次热更新检查，提升用户体验
    if not checkIsDebug() then
        print("在UI加载前进行热更检查...")
        client:checkAndUpdate()
    end
    
    --根据skipUI标志决定是否显示UI界面
    if not _G.skipUI then
        uiConfig = ConfigurationInterface()
    else
        print("开发模式：跳过UI界面")
        -- 开发模式下跳过UI，不进行UI配置合并，保持默认配置
        uiConfig = nil
    end

    --在非调试模式下进行验证卡密
    if not checkIsDebug() then
        if uiConfig and type(uiConfig) == "table" and uiConfig.card_key and uiConfig.card_key ~= "" then
            client:setCardKey(uiConfig.card_key)
        end
        client:startCardVerify()
    end

    -- 处理配置合并
    local config = require("config.config")
    if uiConfig then
        print("开始合并UI配置")
        config.updateFromUIConfig(uiConfig)
        print("UI配置合并完成")
    else
        print("使用预设配置，跳过UI配置合并")
    end
    
    --初始化PaddleOcr的ONNX模型
    initPaddleOcr()

    --设置随机数种子
    math.randomseed(os.time())

    --初始化完成，配置已通过引用更新，后续可直接require获取
    print("所有初始化完成，脚本开始运行。")
end



return main()