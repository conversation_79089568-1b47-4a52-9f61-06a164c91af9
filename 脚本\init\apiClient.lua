--[[
  重构版本的API客户端类 - 支持三次验证失败后退出 + 界面配置上传下载
  使用方式：
  local client = ApiClient.new({
    base_url = "http://47.94.99.3/api/client",
    app_id = "APP_MCN5ZRD9_5G5ZYU",
    crypto_key = "b001c337e87db66606c623e8ad830de8",
    card_key = "LTSAMFWX9WKJUHMP",
    machine_code = "TEST_MACHINE_001",
    version = "1.0.0",
    heartbeat_interval = 3600,
    max_heartbeat_fails = 3  -- 最大允许失败次数
  })

  -- 热更新
  client:checkAndUpdate()

  -- 启动卡密验证和心跳机制
  client:startCardVerify()

  -- 获取配置
  local config = client:loadConfig({"map.json"})

  -- 上传界面配置
  client:uploadUIConfig("my_config.json", config_content)

  -- 下载界面配置
  local ui_config = client:downloadUIConfig("my_config.json")
]]

import('java.lang.*')
import('java.util.*')
import('com.nx.assist.lua.LuaEngine')

-- ===============================================================
-- ApiClient 类定义
-- ===============================================================
local ApiClient = {}
ApiClient.__index = ApiClient

---
-- 创建新的API客户端实例
-- @param config (table) 配置参数
--   - base_url: API基础URL
--   - app_id: 应用ID
--   - crypto_key: 加密密钥
--   - card_key: 卡密
--   - machine_code: 机器码
--   - version: 当前版本号
--   - heartbeat_interval: 心跳验证间隔（秒，可选，默认60）
--   - max_heartbeat_fails: 最大允许失败次数（可选，默认3）
-- @return ApiClient实例
function ApiClient.new(config)
    local self = setmetatable({}, ApiClient)

    -- 必需参数检查
    if not config or not config.base_url or not config.app_id or
            not config.crypto_key or not config.card_key or not config.machine_code or
            not config.version then
        error("ApiClient.new(): 缺少必需的配置参数")
    end

    self.base_url = config.base_url
    self.app_id = config.app_id
    self.crypto_key = config.crypto_key
    self.card_key = config.card_key
    self.machine_code = config.machine_code
    self.version = config.version
    self.heartbeat_interval = config.heartbeat_interval or 60  -- 默认60秒

    -- 新增：心跳验证失败计数器
    self.heartbeat_fail_count = 0
    self.max_heartbeat_fails = config.max_heartbeat_fails or 3  -- 默认3次

    return self
end

-- ===============================================================
-- 内部工具函数
-- ===============================================================

---
-- 生成16字节随机IV
function ApiClient:_generateIV()
    local bytes = {}
    for i = 1, 16 do
        bytes[i] = string.char(math.random(0, 255))
    end
    return table.concat(bytes)
end

---
-- 加密数据
function ApiClient:_encryptData(data_table)
    -- 添加时间戳
    data_table["timestamp"] = os.time()

    -- 序列化为JSON
    local json_str = jsonLib.encode(data_table)

    -- 生成IV并加密
    local iv = self:_generateIV()
    local ciphertext = cryptLib.aes_crypt(json_str, self.crypto_key, "encrypt", "cbc", iv, true)

    -- 拼接IV和密文，然后Base64编码
    local encrypted_payload = iv .. ciphertext
    return encodeBase64(encrypted_payload)
end

---
-- 解密数据
function ApiClient:_decryptData(b64_ciphertext)
    local encrypted_payload = decodeBase64(b64_ciphertext)
    if not encrypted_payload or #encrypted_payload < 17 then
        print("解密错误: Base64解码后的数据长度不足")
        return nil
    end

    -- 分离IV和密文
    local iv = string.sub(encrypted_payload, 1, 16)
    local actual_ciphertext = string.sub(encrypted_payload, 17)

    -- 解密
    local json_str = cryptLib.aes_crypt(actual_ciphertext, self.crypto_key, "decrypt", "cbc", iv, true)
    if not json_str then
        print("解密错误: AES解密失败")
        return nil
    end
    return jsonLib.decode(json_str)
end

---
-- 发送API请求
function ApiClient:_makeRequest(endpoint, data)
    -- 加密数据
    local encrypted_data_str = self:_encryptData(data)

    -- 构造请求体
    local request_table = {
        app_id = self.app_id,
        encrypted_data = encrypted_data_str
    }
    local request_body_json = jsonLib.encode(request_table)

    -- 发送请求
    local url = self.base_url .. "/" .. endpoint
    local response_json = LuaEngine.httpPostData(url, request_body_json, "application/json;charset=utf-8", 10)

    if not response_json or response_json == "" then
        print("请求错误: HTTP请求失败")
        return { status = "error", message = "HTTP request failed" }
    end

    -- 解析响应
    local result = jsonLib.decode(response_json)

    -- 解密响应数据
    if result and result.status == "success" and result.encrypted_data then
        result.decrypted_data = self:_decryptData(result.encrypted_data)
    end

    return result
end

-- ===============================================================
-- 公共API函数
-- ===============================================================

---
-- 检查版本并执行热更新
-- @return (boolean) 是否需要更新
function ApiClient:checkAndUpdate()
    print("正在检查版本更新...")

    local result = self:_makeRequest("version-check", {
        app_id = self.app_id,
        version = self.version
    })

    if not result or not result.decrypted_data then
        print("版本检查失败: 无法获取服务器响应")
        return false
    end

    local data = result.decrypted_data

    if data.need_update then
        print("发现新版本: " .. tostring(data.latest_version))
        toast("发现新版本: " .. tostring(data.latest_version),0,0,12)
        print("开始下载更新...")

        if data.update_url then
            -- 下载进度回调函数
            function progress(pos)
                toast("下载进度:" .. pos, 0, 0, 12)
            end

            -- 下载更新文件
            local download_success = downloadFile(data.update_url, "/mnt/sdcard/update.lrj",progress)

            if download_success then
                print("下载完成，正在安装...")
                toast("正在安装更新...", 0, 0, 12)

                -- 安装更新包
                local install_success = installLrPkg("/mnt/sdcard/update.lrj")

                if install_success then
                    toast("更新完成！", 0, 0, 12)
                    return true
                else
                    toast("更新安装失败", 0, 0, 12)
                    return false
                end
            else
                toast("下载更新失败", 0, 0, 12)
                return false
            end
        else
            print("服务器未提供更新地址")
            return false
        end
    else
        print("当前是最新版本")
        toast("当前是最新版本", 0, 0, 12)
        return false
    end
end

---
-- 卡密验证（内部方法）
-- @return (boolean) 验证是否成功
function ApiClient:_doCardVerify()
    local result = self:_makeRequest("card-verify", {
        app_id = self.app_id,
        card_key = self.card_key,
        machine_code = self.machine_code
    })

    if not result or not result.decrypted_data then
        print("卡密验证失败: 无法获取服务器响应")
        return false
    end

    local data = result.decrypted_data

    if data.valid then
        print("卡密验证成功！")

        if data.card_info then
            local card_info = data.card_info
            print("最大设备数: " .. tostring(card_info.max_devices))
            print("当前设备数: " .. tostring(card_info.bound_devices))
        end

        return true
    else
        print("卡密验证失败: " .. tostring(data.error_message))
        return false
    end
end

---
-- 启动卡密验证和心跳机制
-- @return (boolean) 初始验证是否成功
function ApiClient:startCardVerify()
    print("正在启动卡密验证...")

    -- 首次验证
    local success = self:_doCardVerify()

    if success then
        print("授权验证成功，程序可以正常运行")
        print("启动心跳验证机制，间隔: " .. self.heartbeat_interval .. " 秒")
        print("最大允许失败次数: " .. self.max_heartbeat_fails .. " 次")

        -- 心跳验证回调函数
        local function heartbeat_callback()
            print("执行心跳验证...")
            local verify_success = self:_doCardVerify()

            if verify_success then
                -- 验证成功，重置失败计数器
                if self.heartbeat_fail_count > 0 then
                    print("心跳验证成功，重置失败计数器")
                    self.heartbeat_fail_count = 0
                end
                -- 继续下一次心跳
                setTimer(heartbeat_callback, self.heartbeat_interval * 1000)
            else
                -- 验证失败，增加计数器
                self.heartbeat_fail_count = self.heartbeat_fail_count + 1
                print("心跳验证失败，当前失败次数: " .. self.heartbeat_fail_count .. "/" .. self.max_heartbeat_fails)

                if self.heartbeat_fail_count >= self.max_heartbeat_fails then
                    -- 达到最大失败次数，停止程序
                    print("连续验证失败达到 " .. self.max_heartbeat_fails .. " 次，停止运行")
                    toast("授权已过期，程序即将退出", 0, 0, 12)
                    sleep(1000)
                    exitScript()
                else
                    -- 还有重试机会，继续下一次心跳
                    local remaining = self.max_heartbeat_fails - self.heartbeat_fail_count
                    print("继续尝试，剩余机会: " .. remaining)
                    setTimer(heartbeat_callback, self.heartbeat_interval * 1000)
                end
            end
        end

        -- 启动心跳定时器
        setTimer(heartbeat_callback, self.heartbeat_interval * 1000)

        return true
    else
        print("初始卡密验证失败，无法启动程序")
        toast("授权验证失败，程序即将退出", 0, 0, 12)
        sleep(1000)
        exitScript()
    end
end

---
-- 加载配置
-- @param config_names (table) 配置文件名列表
-- @return (table|false) 配置数据或false
function ApiClient:loadConfig(config_names)
    print("正在加载配置...")

    local result = self:_makeRequest("config-load", {
        app_id = self.app_id,
        config_names = config_names
    })

    if not result or not result.decrypted_data then
        print("加载配置失败: 无法获取服务器响应")
        return false
    end

    local data = result.decrypted_data
    local configs = data.configs

    if not configs then
        print("服务器返回空配置")
        return false
    end

    -- 转换JSON字符串为表（如果需要）
    local processed_configs = {}
    for name, config_data in pairs(configs) do
        if type(config_data) == "string" then
            -- 如果是JSON字符串，解析为表
            local success, parsed_config = pcall(jsonLib.decode, config_data)
            if success then
                processed_configs[name] = parsed_config
            else
                print("解析配置失败: " .. name)
                processed_configs[name] = config_data
            end
        else
            processed_configs[name] = config_data
        end
    end

    print("配置加载成功，共 " .. #config_names .. " 个配置文件")
    return processed_configs
end

---
-- 上传界面配置
-- @param config_name (string) 配置文件名
-- @param config_content (string) 配置内容（JSON字符串）
-- @return (boolean, string) 是否成功, 响应消息
function ApiClient:uploadUIConfig(config_name, config_content)
    print("正在上传界面配置: " .. config_name)
    print("配置大小: " .. #config_content .. " 字节")

    local result = self:_makeRequest("ui-config-upload", {
        app_id = self.app_id,
        config_name = config_name,
        config_content = config_content
    })

    if not result then
        print("上传失败: 无法获取服务器响应")
        return false, "HTTP请求失败"
    end

    if result.status == "success" and result.decrypted_data then
        local data = result.decrypted_data
        print("上传成功: " .. tostring(data.message))
        return true, data.message
    else
        local error_msg = result.message or "未知错误"
        print("上传失败: " .. error_msg)
        return false, error_msg
    end
end

---
-- 下载界面配置
-- @param config_name (string) 配置文件名
-- @return (string|false, string) 配置内容或false, 响应消息
function ApiClient:downloadUIConfig(config_name)
    print("正在下载界面配置: " .. config_name)

    local result = self:_makeRequest("ui-config-download", {
        app_id = self.app_id,
        config_name = config_name
    })

    if not result then
        print("下载失败: 无法获取服务器响应")
        return false, "HTTP请求失败"
    end

    if result.status == "success" and result.decrypted_data then
        local data = result.decrypted_data

        if data.found then
            local content_size = #data.config_content
            print("下载成功: " .. config_name .. " (大小: " .. content_size .. " 字节)")
            return data.config_content, "下载成功"
        else
            print("配置未找到: " .. config_name)
            return false, "配置文件不存在"
        end
    else
        local error_msg = result.message or "未知错误"
        print("下载失败: " .. error_msg)
        return false, error_msg
    end
end

---
-- 设置卡密属性
-- @param new_card_key (string) 新的卡密
-- @return (boolean) 是否设置成功
function ApiClient:setCardKey(new_card_key)
    if not new_card_key or type(new_card_key) ~= "string" or new_card_key == "" then
        print("设置卡密失败: 卡密不能为空")
        return false
    end

    local old_card_key = self.card_key
    self.card_key = new_card_key
    print("卡密已更新: " .. old_card_key .. " -> " .. new_card_key)
    return true
end


-- ===============================================================
-- 使用示例
-- ===============================================================

-- 创建客户端实例
--local client = ApiClient.new({
--    base_url = "http://47.94.99.3/api/client",
--    app_id = "APP_MCN5ZRD9_5G5ZYU",
--    crypto_key = "b001c337e87db66606c623e8ad830de8",
--    card_key = "LTSAMFWX9WKJUHMP",
--    machine_code = "TEST_MACHINE_001",
--    version = "1.0.2",
--    heartbeat_interval = 60,
--    max_heartbeat_fails = 3
--})

-- 使用示例（注释掉，实际使用时取消注释）

---- 1. 检查并更新版本
--client:checkAndUpdate()
--
---- 2. 启动卡密验证和心跳机制
--client:startCardVerify()
--
---- 3. 加载配置
--local configs = client:loadConfig({"map.json", "settings.json"})
--if configs then
--    print("配置加载成功:")
--    for name, config in pairs(configs) do
--        print(name .. ":", jsonLib.encode(config))
--    end
--end
--

-- 返回类，供外部使用
return ApiClient