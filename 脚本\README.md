# 五霸争雄游戏自动化脚本

## 概述

这是一个为"五霸争雄"游戏开发的自动化脚本系统，实现了完整的挂机、定时任务和自动重启功能。

## 核心功能

### 1. 主线挂机逻辑
- 自动进入配置的地图
- 开启目标选择怪物界面
- 自动攻击第一个目标怪物
- 开启自动战斗功能
- 根据配置进行定时喊话

### 2. 定时重启机制
- 支持自定义重启间隔时间（分钟）
- 自动关闭游戏应用
- 等待6秒后重新启动游戏
- 重新加载游戏并进入地图

### 3. 定时任务系统

#### 周期性任务
- **禁地**: 每6小时执行一次
- **世界首领**: 每15分钟执行一次（当天失败2次后停止）

#### 每日定时任务
- **22:00**: 领取宝箱和散人福利
- **20:00**: 星空秘境
- **13:00 & 21:00**: 武林争霸

#### 启动时一次性任务
- **个人首领**: 脚本启动时执行一次
- **入道天途**: 脚本启动时执行一次

### 4. 状态管理
- 智能进图状态管理
- 任务执行后自动重新进图
- 每日任务状态自动重置

## 配置说明

### 用户配置 (config.userConfig)

```lua
-- 定时重启设置
定时重启 = {
    enable = true,      -- 是否启用定时重启
    重启时间 = 30       -- 重启间隔（分钟）
},

-- 地图配置
map_enabled = { enable = true },  -- 是否启用进入地图功能
map = "时空幽谷",                 -- 地图名称

-- 喊话设置
喊话设置 = {
    喊话1 = {
        enabled = true,                    -- 是否启用
        content = "收购各种材料，价格优惠！", -- 喊话内容
        interval = 15                      -- 间隔秒数
    },
    喊话2 = {
        enabled = true,
        content = "出售高级装备，物美价廉！",
        interval = 20
    },
},

-- 各种任务开关
禁地 = { enable = true },
世界Boss = { enable = true },
个人首领 = { enable = true },
入道天途 = { enable = true },
升级宝箱 = { enable = true },
散人福利 = { enable = true },
星空秘境 = { enable = true },
武林争霸 = { enable = true }
```

## 使用方法

### 1. 正常启动
```lua
-- 运行主脚本
require("五霸争雄")
```

### 2. 测试模式
```lua
-- 运行测试脚本
require("test_automation")
```

### 3. 调试模式
```lua
-- 设置调试标志
_G.isDebug = true
_G.skipUI = true
require("五霸争雄")
```

## 文件结构

```
脚本/
├── 五霸争雄.lua          # 主入口文件
├── character.lua         # 游戏操作接口
├── test_automation.lua   # 测试文件
├── core/
│   └── automation.lua    # 核心自动化系统
├── init/
│   ├── init.lua         # 初始化模块
│   ├── utils.lua        # 工具函数
│   ├── common.lua       # 通用函数
│   ├── ui.lua           # UI界面
│   └── apiClient.lua    # API客户端
└── config/
    └── config.lua       # 配置文件
```

## 注意事项

1. **权限要求**: 确保脚本有足够的系统权限
2. **游戏版本**: 确保游戏版本与脚本配置匹配
3. **网络连接**: 某些功能需要网络连接
4. **设备性能**: 建议在性能较好的设备上运行
5. **安全使用**: 请遵守游戏规则，合理使用自动化功能

## 故障排除

### 常见问题

1. **进图失败**
   - 检查地图名称配置是否正确
   - 确认游戏界面状态正常

2. **重启失败**
   - 检查应用包名配置
   - 确认设备权限设置

3. **喊话失败**
   - 检查输入法权限
   - 确认喊话内容格式正确

4. **任务执行失败**
   - 检查对应功能的UI配置
   - 确认游戏界面元素位置

## 更新日志

### v1.0.0
- 实现核心自动化系统
- 支持主线挂机功能
- 添加定时重启机制
- 实现完整的定时任务系统
- 添加状态管理和错误处理

## 技术支持

如遇到问题，请检查：
1. 配置文件设置
2. 游戏界面状态
3. 设备权限设置
4. 脚本日志输出
