-- ======================================================================
-- 自动化系统测试文件
-- 用于测试核心自动化系统的各个功能模块
-- ======================================================================

-- 设置调试模式
_G.isDebug = true
_G.skipUI = true

-- 加载必要的模块
require("init.init")
local utils = require("init.utils")
local automation = require("core.automation")

-- 启动全局提示器
_G.hudManager = utils.HUDManager:new()

print("=== 自动化系统测试开始 ===")

-- 测试配置加载
local config = require("config.config")
print("用户配置加载测试:")
print("- 定时重启启用:", config.userConfig.定时重启.enable)
print("- 定时重启时间:", config.userConfig.定时重启.重启时间, "分钟")
print("- 地图配置:", config.userConfig.map)
print("- 喊话设置数量:", #config.userConfig.喊话设置 or 0)

-- 测试character模块
local character = require("character")
print("\ncharacter模块测试:")
print("- closeApp方法存在:", type(character.closeApp) == "function")
print("- 进入打宝地图方法存在:", type(character.进入打宝地图) == "function")
print("- 执行喊话方法存在:", type(character.执行喊话) == "function")

-- 测试utils模块
print("\nutils模块测试:")
print("- Timer类存在:", type(utils.Timer) == "table")
print("- close_app函数存在:", type(utils.close_app) == "function")
print("- start_app函数存在:", type(utils.start_app) == "function")

-- 创建自动化系统实例进行基本测试
print("\n=== 创建自动化系统实例 ===")
local automationFunc = automation()

print("自动化系统创建成功!")
print("注意: 在实际运行中，请确保:")
print("1. 游戏应用已正确安装")
print("2. 相关UI配置已正确设置")
print("3. 网络连接正常")
print("4. 设备权限已授予")

print("\n=== 测试完成 ===")
print("如需运行完整自动化系统，请运行主脚本文件")
